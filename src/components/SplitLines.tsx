import React from 'react';
import { SplitLinesProps } from '../types';

/**
 * Component that renders the red split lines that follow the mouse cursor
 */
export const SplitLines: React.FC<SplitLinesProps> = ({ mousePos }) => {
  return (
    <>
      {/* Vertical split line */}
      <div
        className="absolute bg-red-500 opacity-50 pointer-events-none z-10"
        style={{
          left: mousePos.x,
          top: 0,
          width: '1px',
          height: '100%'
        }}
      />
      {/* Horizontal split line */}
      <div
        className="absolute bg-red-500 opacity-50 pointer-events-none z-10"
        style={{
          left: 0,
          top: mousePos.y,
          width: '100%',
          height: '1px'
        }}
      />
    </>
  );
};
