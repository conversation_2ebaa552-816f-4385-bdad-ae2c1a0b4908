import React, { useState, useRef, useCallback } from 'react';
import { Pill } from '../types';
import { useMousePosition, useDrawing, useDragging, usePillSplitting } from '../hooks';
import { SplitLines, PillComponent, DrawingPreview, Instructions } from './';

/**
 * Main canvas component that handles all pill interactions
 */
export const PillCanvas: React.FC = () => {
  const [pills, setPills] = useState<Pill[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Custom hooks
  const { mousePos, updateMousePosition } = useMousePosition();
  const { isDrawing, drawStart, startDrawing, finishDrawing } = useDrawing();
  const { 
    isDragging, 
    startDragging, 
    updateDraggedPill, 
    finishDragging, 
    findClickedPill 
  } = useDragging();
  const { splitPills } = usePillSplitting();

  /**
   * <PERSON>les mouse movement for updating position and dragging
   */
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    updateMousePosition(containerRef, e);

    if (isDragging) {
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        const position = {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        };
        updateDraggedPill(position, pills, setPills);
      }
    }
  }, [updateMousePosition, isDragging, updateDraggedPill, pills]);

  /**
   * Handles mouse down for starting drawing or dragging
   */
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const clickPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    // Check if clicking on a pill for dragging
    const clickedPill = findClickedPill(pills, clickPosition);

    if (clickedPill) {
      const offset = {
        x: clickPosition.x - clickedPill.x,
        y: clickPosition.y - clickedPill.y
      };
      startDragging(clickedPill.id, offset);
    } else {
      // Start drawing a new pill
      startDrawing(clickPosition);
    }
  }, [pills, findClickedPill, startDragging, startDrawing]);

  /**
   * Handles mouse up for completing drawing or dragging
   */
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const endPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    if (isDrawing) {
      finishDrawing(endPosition, (newPill: Pill) => {
        setPills((prev: Pill[]) => [...prev, newPill]);
      });
    } else if (isDragging) {
      finishDragging();
    }
  }, [isDrawing, isDragging, finishDrawing, finishDragging]);

  /**
   * Handles clicks for splitting pills
   */
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (isDrawing || isDragging) return;

    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const clickPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    splitPills(pills, clickPosition, setPills);
  }, [pills, isDrawing, isDragging, splitPills]);

  return (
    <div
      ref={containerRef}
      className="w-screen h-screen bg-gray-100 relative overflow-hidden cursor-crosshair"
      onMouseMove={handleMouseMove}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onClick={handleClick}
    >
      <SplitLines mousePos={mousePos} />
      
      {/* Pills */}
      {pills.map((pill: Pill) => (
        <PillComponent key={pill.id} pill={pill} />
      ))}

      <DrawingPreview 
        isDrawing={isDrawing} 
        drawStart={drawStart} 
        mousePos={mousePos} 
      />

      <Instructions />
    </div>
  );
};
