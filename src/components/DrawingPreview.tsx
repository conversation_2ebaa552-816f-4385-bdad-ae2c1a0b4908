import React from 'react';
import { DrawingPreviewProps } from '../types';

/**
 * Component that shows a preview of the pill being drawn
 */
export const DrawingPreview: React.FC<DrawingPreviewProps> = ({ 
  isDrawing, 
  drawStart, 
  mousePos 
}) => {
  if (!isDrawing) return null;

  return (
    <div
      className="absolute border-2 border-dashed border-gray-600 pointer-events-none"
      style={{
        left: Math.min(drawStart.x, mousePos.x),
        top: Math.min(drawStart.y, mousePos.y),
        width: Math.abs(mousePos.x - drawStart.x),
        height: Math.abs(mousePos.y - drawStart.y),
        borderRadius: '20px'
      }}
    />
  );
};
