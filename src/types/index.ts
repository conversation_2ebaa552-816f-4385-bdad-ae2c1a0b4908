/**
 * Represents a position with x and y coordinates
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * Represents a pill or pill part with position, size, and styling
 */
export interface Pill {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  borderRadius: string;
}

/**
 * Props for mouse event handlers
 */
export interface MouseEventHandlers {
  onMouseMove: (e: React.MouseEvent) => void;
  onMouseDown: (e: React.MouseEvent) => void;
  onMouseUp: (e: React.MouseEvent) => void;
  onClick: (e: React.MouseEvent) => void;
}

/**
 * Props for pill-related components
 */
export interface PillComponentProps {
  pill: Pill;
}

/**
 * Props for split lines component
 */
export interface SplitLinesProps {
  mousePos: Position;
}

/**
 * Props for drawing preview component
 */
export interface DrawingPreviewProps {
  isDrawing: boolean;
  drawStart: Position;
  mousePos: Position;
}

/**
 * Props for instructions component
 */
export interface InstructionsProps {
  className?: string;
}
