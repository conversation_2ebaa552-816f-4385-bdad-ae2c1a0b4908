import { Pill } from '../types';

/**
 * Generates a random color for pills
 */
export const generateRandomColor = (): string => {
  const colors = [
    '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
    '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
    '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
    '#ec4899', '#f43f5e'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

/**
 * Generates a unique ID for pills
 */
export const generateId = (): string => Math.random().toString(36).substring(2, 11);

/**
 * Calculates border radius for split pills
 */
export const calculateBorderRadius = (
  pill: Pill,
  splitX: number,
  _splitY: number,
  isLeft: boolean,
  isTop: boolean
): string => {
  const radius = 20;
  let topLeft = 0, topRight = 0, bottomRight = 0, bottomLeft = 0;

  // Parse original border radius
  const originalRadius = pill.borderRadius.includes('px') ?
    parseInt(pill.borderRadius.replace('px', '')) : radius;

  if (splitX !== -1) { // Vertical split
    if (isLeft) {
      topLeft = originalRadius;
      bottomLeft = originalRadius;
    } else {
      topRight = originalRadius;
      bottomRight = originalRadius;
    }
  } else { // Horizontal split
    if (isTop) {
      topLeft = originalRadius;
      topRight = originalRadius;
    } else {
      bottomLeft = originalRadius;
      bottomRight = originalRadius;
    }
  }

  return `${topLeft}px ${topRight}px ${bottomRight}px ${bottomLeft}px`;
};
