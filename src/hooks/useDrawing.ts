import { useState, useCallback } from 'react';
import { Position, Pill } from '../types';
import { generateId, generateRandomColor } from '../utils';

/**
 * Hook for managing pill drawing functionality
 */
export const useDrawing = () => {
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawStart, setDrawStart] = useState<Position>({ x: 0, y: 0 });

  const startDrawing = useCallback((position: Position) => {
    setIsDrawing(true);
    setDrawStart(position);
  }, []);

  const finishDrawing = useCallback((
    endPosition: Position,
    onPillCreated: (pill: Pill) => void
  ) => {
    if (!isDrawing) return;

    const width = Math.abs(endPosition.x - drawStart.x);
    const height = Math.abs(endPosition.y - drawStart.y);

    // Only create pill if it meets minimum size
    if (width >= 40 && height >= 40) {
      const newPill: Pill = {
        id: generateId(),
        x: Math.min(drawStart.x, endPosition.x),
        y: Math.min(drawStart.y, endPosition.y),
        width,
        height,
        color: generateRandomColor(),
        borderRadius: '20px'
      };
      onPillCreated(newPill);
    }
    setIsDrawing(false);
  }, [isDrawing, drawStart]);

  const cancelDrawing = useCallback(() => {
    setIsDrawing(false);
  }, []);

  return {
    isDrawing,
    drawStart,
    startDrawing,
    finishDrawing,
    cancelDrawing
  };
};
