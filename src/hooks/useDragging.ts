import { useState, useCallback } from 'react';
import { Position, Pill } from '../types';

/**
 * Hook for managing pill dragging functionality
 */
export const useDragging = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedPill, setDraggedPill] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });

  const startDragging = useCallback((pillId: string, offset: Position) => {
    setIsDragging(true);
    setDraggedPill(pillId);
    setDragOffset(offset);
  }, []);

  const updateDraggedPill = useCallback((
    position: Position,
    pills: Pill[],
    onPillsUpdate: (pills: Pill[]) => void
  ) => {
    if (!isDragging || !draggedPill) return;

    const updatedPills = pills.map((pill: Pill) =>
      pill.id === draggedPill
        ? {
            ...pill,
            x: position.x - dragOffset.x,
            y: position.y - dragOffset.y
          }
        : pill
    );
    onPillsUpdate(updatedPills);
  }, [isDragging, draggedPill, dragOffset]);

  const finishDragging = useCallback(() => {
    setIsDragging(false);
    setDraggedPill(null);
  }, []);

  const findClickedPill = useCallback((pills: Pill[], clickPosition: Position): Pill | undefined => {
    return pills.find((pill: Pill) =>
      clickPosition.x >= pill.x && clickPosition.x <= pill.x + pill.width &&
      clickPosition.y >= pill.y && clickPosition.y <= pill.y + pill.height
    );
  }, []);

  return {
    isDragging,
    draggedPill,
    dragOffset,
    startDragging,
    updateDraggedPill,
    finishDragging,
    findClickedPill
  };
};
