import { useState, useCallback } from 'react';
import { Position } from '../types';

/**
 * Hook for managing mouse position
 */
export const useMousePosition = () => {
  const [mousePos, setMousePos] = useState<Position>({ x: 0, y: 0 });

  const updateMousePosition = useCallback((containerRef: React.RefObject<HTMLDivElement>, e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    setMousePos({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  }, []);

  return {
    mousePos,
    updateMousePosition
  };
};
