import { useCallback } from 'react';
import { Position, Pill } from '../types';
import { generateId, calculateBorderRadius } from '../utils';

/**
 * Hook for managing pill splitting functionality
 */
export const usePillSplitting = () => {
  const splitPills = useCallback((
    pills: Pill[],
    clickPosition: Position,
    onPillsUpdate: (pills: Pill[]) => void
  ) => {
    const { x: clickX, y: clickY } = clickPosition;

    // Find pills that intersect with split lines
    const pillsToSplit = pills.filter((pill: Pill) => {
      const intersectsVertical = clickX >= pill.x && clickX <= pill.x + pill.width;
      const intersectsHorizontal = clickY >= pill.y && clickY <= pill.y + pill.height;
      return intersectsVertical || intersectsHorizontal;
    });

    if (pillsToSplit.length === 0) return;

    const newPills = [...pills];

    pillsToSplit.forEach((pill: Pill) => {
      const pillIndex = newPills.findIndex(p => p.id === pill.id);
      if (pillIndex === -1) return;

      // Remove original pill
      newPills.splice(pillIndex, 1);

      const intersectsVertical = clickX >= pill.x && clickX <= pill.x + pill.width;
      const intersectsHorizontal = clickY >= pill.y && clickY <= pill.y + pill.height;

      if (intersectsVertical && intersectsHorizontal) {
        // Split both ways - create 4 parts
        const leftWidth = clickX - pill.x;
        const rightWidth = pill.width - leftWidth;
        const topHeight = clickY - pill.y;
        const bottomHeight = pill.height - topHeight;

        // Only create parts that meet minimum size
        if (leftWidth >= 20 && topHeight >= 20) {
          newPills.push({
            ...pill,
            id: generateId(),
            width: leftWidth,
            height: topHeight,
            borderRadius: calculateBorderRadius(pill, clickX, clickY, true, true)
          });
        }
        if (rightWidth >= 20 && topHeight >= 20) {
          newPills.push({
            ...pill,
            id: generateId(),
            x: clickX,
            width: rightWidth,
            height: topHeight,
            borderRadius: calculateBorderRadius(pill, clickX, clickY, false, true)
          });
        }
        if (leftWidth >= 20 && bottomHeight >= 20) {
          newPills.push({
            ...pill,
            id: generateId(),
            y: clickY,
            width: leftWidth,
            height: bottomHeight,
            borderRadius: calculateBorderRadius(pill, clickX, clickY, true, false)
          });
        }
        if (rightWidth >= 20 && bottomHeight >= 20) {
          newPills.push({
            ...pill,
            id: generateId(),
            x: clickX,
            y: clickY,
            width: rightWidth,
            height: bottomHeight,
            borderRadius: calculateBorderRadius(pill, clickX, clickY, false, false)
          });
        }
      } else if (intersectsVertical) {
        // Vertical split
        const leftWidth = clickX - pill.x;
        const rightWidth = pill.width - leftWidth;

        if (leftWidth >= 20 && rightWidth >= 20) {
          // Both parts are large enough
          newPills.push({
            ...pill,
            id: generateId(),
            width: leftWidth,
            borderRadius: calculateBorderRadius(pill, clickX, -1, true, false)
          });
          newPills.push({
            ...pill,
            id: generateId(),
            x: clickX,
            width: rightWidth,
            borderRadius: calculateBorderRadius(pill, clickX, -1, false, false)
          });
        } else {
          // Move to the side with more space
          const moveLeft = rightWidth < leftWidth;
          newPills.push({
            ...pill,
            id: generateId(),
            x: moveLeft ? pill.x - 10 : pill.x + 10
          });
        }
      } else if (intersectsHorizontal) {
        // Horizontal split
        const topHeight = clickY - pill.y;
        const bottomHeight = pill.height - topHeight;

        if (topHeight >= 20 && bottomHeight >= 20) {
          // Both parts are large enough
          newPills.push({
            ...pill,
            id: generateId(),
            height: topHeight,
            borderRadius: calculateBorderRadius(pill, -1, clickY, false, true)
          });
          newPills.push({
            ...pill,
            id: generateId(),
            y: clickY,
            height: bottomHeight,
            borderRadius: calculateBorderRadius(pill, -1, clickY, false, false)
          });
        } else {
          // Move to the side with more space
          const moveUp = bottomHeight < topHeight;
          newPills.push({
            ...pill,
            id: generateId(),
            y: moveUp ? pill.y - 10 : pill.y + 10
          });
        }
      }
    });

    onPillsUpdate(newPills);
  }, []);

  return {
    splitPills
  };
};
